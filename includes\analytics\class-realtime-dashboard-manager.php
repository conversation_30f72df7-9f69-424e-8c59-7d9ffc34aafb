<?php
/**
 * Real-time Analytics Dashboard Manager
 * Phase 3: Data Intelligence & Analytics
 * 
 * Manages real-time data streaming, live dashboard updates,
 * and interactive analytics dashboards with drill-down capabilities.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Realtime_Dashboard_Manager {
    
    /**
     * Initialize the Real-time Dashboard Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_create_realtime_dashboard', array(__CLASS__, 'create_dashboard'));
        add_action('wp_ajax_dab_update_realtime_dashboard', array(__CLASS__, 'update_dashboard'));
        add_action('wp_ajax_dab_get_realtime_data', array(__CLASS__, 'get_realtime_data'));
        add_action('wp_ajax_dab_add_dashboard_widget', array(__CLASS__, 'add_widget'));
        add_action('wp_ajax_dab_update_widget_config', array(__CLASS__, 'update_widget_config'));
        add_action('wp_ajax_dab_remove_dashboard_widget', array(__CLASS__, 'remove_widget'));
        add_action('wp_ajax_dab_get_widget_data', array(__CLASS__, 'get_widget_data'));
        add_action('wp_ajax_dab_export_dashboard', array(__CLASS__, 'export_dashboard'));
        add_action('wp_ajax_dab_share_dashboard', array(__CLASS__, 'share_dashboard'));
        
        // Frontend AJAX for public dashboards
        add_action('wp_ajax_nopriv_dab_get_realtime_data', array(__CLASS__, 'get_realtime_data'));
        add_action('wp_ajax_nopriv_dab_get_widget_data', array(__CLASS__, 'get_widget_data'));
        
        // WebSocket support for real-time updates
        add_action('wp_footer', array(__CLASS__, 'add_realtime_scripts'));
        add_action('admin_footer', array(__CLASS__, 'add_realtime_scripts'));
        
        // Data change hooks for real-time updates
        add_action('dab_data_inserted', array(__CLASS__, 'trigger_realtime_update'), 10, 3);
        add_action('dab_data_updated', array(__CLASS__, 'trigger_realtime_update'), 10, 3);
        add_action('dab_data_deleted', array(__CLASS__, 'trigger_realtime_update'), 10, 3);
    }
    
    /**
     * Create database tables for real-time dashboards
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        // Real-time dashboards table
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';
        $sql = "CREATE TABLE IF NOT EXISTS $dashboards_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            layout_config LONGTEXT,
            theme_config TEXT,
            refresh_interval INT DEFAULT 30,
            auto_refresh TINYINT(1) DEFAULT 1,
            is_public TINYINT(1) DEFAULT 0,
            public_key VARCHAR(32),
            access_permissions TEXT,
            created_by BIGINT(20) UNSIGNED,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_accessed DATETIME NULL,
            access_count INT DEFAULT 0,
            PRIMARY KEY (id),
            UNIQUE KEY unique_public_key (public_key),
            KEY idx_created_by (created_by),
            KEY idx_is_public (is_public)
        ) $charset_collate;";
        
        // Dashboard widgets table
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        $sql .= "CREATE TABLE IF NOT EXISTS $widgets_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            dashboard_id BIGINT(20) UNSIGNED NOT NULL,
            widget_type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            position_x INT DEFAULT 0,
            position_y INT DEFAULT 0,
            width INT DEFAULT 4,
            height INT DEFAULT 3,
            data_source VARCHAR(100),
            query_config LONGTEXT,
            visualization_config LONGTEXT,
            filter_config TEXT,
            refresh_interval INT DEFAULT 30,
            is_active TINYINT(1) DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_dashboard_id (dashboard_id),
            KEY idx_widget_type (widget_type),
            KEY idx_is_active (is_active),
            FOREIGN KEY (dashboard_id) REFERENCES $dashboards_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        // Real-time data cache table
        $cache_table = $wpdb->prefix . 'dab_realtime_cache';
        $sql .= "CREATE TABLE IF NOT EXISTS $cache_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            cache_key VARCHAR(255) NOT NULL,
            data_source VARCHAR(100) NOT NULL,
            cached_data LONGTEXT,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_cache_key (cache_key),
            KEY idx_data_source (data_source),
            KEY idx_expires_at (expires_at)
        ) $charset_collate;";
        
        // Dashboard alerts table
        $alerts_table = $wpdb->prefix . 'dab_dashboard_alerts';
        $sql .= "CREATE TABLE IF NOT EXISTS $alerts_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            dashboard_id BIGINT(20) UNSIGNED NOT NULL,
            widget_id BIGINT(20) UNSIGNED,
            alert_type VARCHAR(50) NOT NULL,
            condition_config TEXT,
            notification_config TEXT,
            is_active TINYINT(1) DEFAULT 1,
            last_triggered DATETIME NULL,
            trigger_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_dashboard_id (dashboard_id),
            KEY idx_widget_id (widget_id),
            KEY idx_is_active (is_active),
            FOREIGN KEY (dashboard_id) REFERENCES $dashboards_table(id) ON DELETE CASCADE,
            FOREIGN KEY (widget_id) REFERENCES $widgets_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Create a new real-time dashboard
     */
    public static function create_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';
        
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $layout_config = $_POST['layout_config'];
        $theme_config = $_POST['theme_config'];
        $refresh_interval = intval($_POST['refresh_interval']);
        $auto_refresh = intval($_POST['auto_refresh']);
        $is_public = intval($_POST['is_public']);
        $access_permissions = $_POST['access_permissions'];
        
        $public_key = $is_public ? wp_generate_password(32, false) : null;
        
        $data = array(
            'name' => $name,
            'description' => $description,
            'layout_config' => json_encode($layout_config),
            'theme_config' => json_encode($theme_config),
            'refresh_interval' => $refresh_interval,
            'auto_refresh' => $auto_refresh,
            'is_public' => $is_public,
            'public_key' => $public_key,
            'access_permissions' => json_encode($access_permissions),
            'created_by' => get_current_user_id()
        );
        
        $result = $wpdb->insert($dashboards_table, $data);
        
        if ($result !== false) {
            $dashboard_id = $wpdb->insert_id;
            
            wp_send_json_success(array(
                'message' => 'Dashboard created successfully',
                'dashboard_id' => $dashboard_id,
                'public_key' => $public_key
            ));
        } else {
            wp_send_json_error('Failed to create dashboard');
        }
    }
    
    /**
     * Update dashboard configuration
     */
    public static function update_dashboard() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $dashboards_table = $wpdb->prefix . 'dab_realtime_dashboards';
        
        $dashboard_id = intval($_POST['dashboard_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $layout_config = $_POST['layout_config'];
        $theme_config = $_POST['theme_config'];
        $refresh_interval = intval($_POST['refresh_interval']);
        $auto_refresh = intval($_POST['auto_refresh']);
        $is_public = intval($_POST['is_public']);
        $access_permissions = $_POST['access_permissions'];
        
        // Generate public key if making dashboard public
        $public_key = null;
        if ($is_public) {
            $existing_key = $wpdb->get_var($wpdb->prepare(
                "SELECT public_key FROM $dashboards_table WHERE id = %d",
                $dashboard_id
            ));
            $public_key = $existing_key ?: wp_generate_password(32, false);
        }
        
        $data = array(
            'name' => $name,
            'description' => $description,
            'layout_config' => json_encode($layout_config),
            'theme_config' => json_encode($theme_config),
            'refresh_interval' => $refresh_interval,
            'auto_refresh' => $auto_refresh,
            'is_public' => $is_public,
            'public_key' => $public_key,
            'access_permissions' => json_encode($access_permissions)
        );
        
        $result = $wpdb->update($dashboards_table, $data, array('id' => $dashboard_id));
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Dashboard updated successfully',
                'public_key' => $public_key
            ));
        } else {
            wp_send_json_error('Failed to update dashboard');
        }
    }
    
    /**
     * Get real-time data for dashboard
     */
    public static function get_realtime_data() {
        $dashboard_id = intval($_POST['dashboard_id']);
        $widget_ids = isset($_POST['widget_ids']) ? array_map('intval', $_POST['widget_ids']) : array();
        $public_key = isset($_POST['public_key']) ? sanitize_text_field($_POST['public_key']) : '';
        
        // Verify access permissions
        if (!self::check_dashboard_access($dashboard_id, $public_key)) {
            wp_send_json_error('Access denied');
            return;
        }
        
        try {
            $data = array();
            
            if (empty($widget_ids)) {
                // Get all widgets for dashboard
                $widget_ids = self::get_dashboard_widget_ids($dashboard_id);
            }
            
            foreach ($widget_ids as $widget_id) {
                $widget_data = self::get_widget_realtime_data($widget_id);
                if ($widget_data) {
                    $data[$widget_id] = $widget_data;
                }
            }
            
            // Update dashboard access tracking
            self::update_dashboard_access($dashboard_id);
            
            wp_send_json_success($data);
        } catch (Exception $e) {
            wp_send_json_error('Failed to get real-time data: ' . $e->getMessage());
        }
    }
    
    /**
     * Add a widget to dashboard
     */
    public static function add_widget() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        global $wpdb;
        $widgets_table = $wpdb->prefix . 'dab_dashboard_widgets';
        
        $dashboard_id = intval($_POST['dashboard_id']);
        $widget_type = sanitize_text_field($_POST['widget_type']);
        $title = sanitize_text_field($_POST['title']);
        $position_x = intval($_POST['position_x']);
        $position_y = intval($_POST['position_y']);
        $width = intval($_POST['width']);
        $height = intval($_POST['height']);
        $data_source = sanitize_text_field($_POST['data_source']);
        $query_config = $_POST['query_config'];
        $visualization_config = $_POST['visualization_config'];
        $filter_config = $_POST['filter_config'];
        $refresh_interval = intval($_POST['refresh_interval']);
        
        $data = array(
            'dashboard_id' => $dashboard_id,
            'widget_type' => $widget_type,
            'title' => $title,
            'position_x' => $position_x,
            'position_y' => $position_y,
            'width' => $width,
            'height' => $height,
            'data_source' => $data_source,
            'query_config' => json_encode($query_config),
            'visualization_config' => json_encode($visualization_config),
            'filter_config' => json_encode($filter_config),
            'refresh_interval' => $refresh_interval
        );
        
        $result = $wpdb->insert($widgets_table, $data);
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Widget added successfully',
                'widget_id' => $wpdb->insert_id
            ));
        } else {
            wp_send_json_error('Failed to add widget');
        }
    }
}
