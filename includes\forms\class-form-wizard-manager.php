<?php
/**
 * Form Wizard Manager
 * 
 * Manages wizard-style form configurations and settings
 * 
 * @package Database App Builder
 * @subpackage Forms
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Form_Wizard_Manager {
    
    /**
     * Initialize the Form Wizard Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_create_wizard_form', array(__CLASS__, 'create_wizard_form'));
        add_action('wp_ajax_dab_update_wizard_config', array(__CLASS__, 'update_wizard_config'));
        add_action('wp_ajax_dab_get_wizard_config', array(__CLASS__, 'get_wizard_config'));
        add_action('wp_ajax_dab_delete_wizard_form', array(__CLASS__, 'delete_wizard_form'));
        
        // Add admin menu for wizard management
        add_action('admin_menu', array(__CLASS__, 'add_admin_menu'));
        
        // Handle form conversion to wizard
        add_action('wp_ajax_dab_convert_to_wizard', array(__CLASS__, 'convert_form_to_wizard'));
    }
    
    /**
     * Add admin menu for wizard management
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'dab_data_management',
            __('Multi-Step Forms', 'db-app-builder'),
            __('Multi-Step Forms', 'db-app-builder'),
            'manage_options',
            'dab_multistep_forms',
            array(__CLASS__, 'render_admin_page')
        );
    }
    
    /**
     * Render admin page for wizard management
     */
    public static function render_admin_page() {
        global $wpdb;
        
        // Get all forms
        $forms_table = $wpdb->prefix . 'dab_forms';
        $forms = $wpdb->get_results("SELECT * FROM $forms_table ORDER BY created_at DESC");
        
        // Get all wizard forms
        $wizard_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        $wizard_forms = $wpdb->get_results("SELECT * FROM $wizard_forms_table ORDER BY created_at DESC");
        
        ?>
        <div class="wrap">
            <h1><?php _e('Multi-Step Forms', 'db-app-builder'); ?></h1>
            
            <div class="dab-wizard-manager">
                <!-- Create New Wizard Form -->
                <div class="dab-wizard-section">
                    <h2><?php _e('Create New Multi-Step Form', 'db-app-builder'); ?></h2>
                    <p><?php _e('Convert an existing form into a multi-step wizard or create a new one.', 'db-app-builder'); ?></p>
                    
                    <form id="dab-create-wizard-form" class="dab-wizard-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="wizard-base-form"><?php _e('Base Form', 'db-app-builder'); ?></label>
                                </th>
                                <td>
                                    <select id="wizard-base-form" name="base_form_id" class="regular-text" required>
                                        <option value=""><?php _e('Select a form', 'db-app-builder'); ?></option>
                                        <?php foreach ($forms as $form): ?>
                                            <option value="<?php echo $form->id; ?>"><?php echo esc_html($form->form_title); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p class="description"><?php _e('Choose the form to convert into a multi-step wizard.', 'db-app-builder'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="wizard-title"><?php _e('Wizard Title', 'db-app-builder'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="wizard-title" name="wizard_title" class="regular-text" required>
                                    <p class="description"><?php _e('Enter a title for this multi-step form.', 'db-app-builder'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="wizard-description"><?php _e('Description', 'db-app-builder'); ?></label>
                                </th>
                                <td>
                                    <textarea id="wizard-description" name="wizard_description" class="large-text" rows="3"></textarea>
                                    <p class="description"><?php _e('Optional description for this wizard form.', 'db-app-builder'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="fields-per-step"><?php _e('Fields per Step', 'db-app-builder'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="fields-per-step" name="fields_per_step" value="5" min="1" max="20" class="small-text">
                                    <p class="description"><?php _e('Maximum number of fields to show per step.', 'db-app-builder'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <h3><?php _e('Wizard Settings', 'db-app-builder'); ?></h3>
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Features', 'db-app-builder'); ?></th>
                                <td>
                                    <fieldset>
                                        <label>
                                            <input type="checkbox" name="show_progress" value="1" checked>
                                            <?php _e('Show progress indicator', 'db-app-builder'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="save_progress" value="1" checked>
                                            <?php _e('Allow users to save progress', 'db-app-builder'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="allow_back" value="1" checked>
                                            <?php _e('Allow going back to previous steps', 'db-app-builder'); ?>
                                        </label><br>
                                        <label>
                                            <input type="checkbox" name="step_validation" value="1" checked>
                                            <?php _e('Validate each step before proceeding', 'db-app-builder'); ?>
                                        </label>
                                    </fieldset>
                                </td>
                            </tr>
                        </table>
                        
                        <?php wp_nonce_field('dab_wizard_manager', 'dab_wizard_nonce'); ?>
                        <p class="submit">
                            <button type="submit" class="button button-primary"><?php _e('Create Multi-Step Form', 'db-app-builder'); ?></button>
                        </p>
                    </form>
                </div>
                
                <!-- Existing Wizard Forms -->
                <div class="dab-wizard-section">
                    <h2><?php _e('Existing Multi-Step Forms', 'db-app-builder'); ?></h2>
                    
                    <?php if (empty($wizard_forms)): ?>
                        <p><?php _e('No multi-step forms created yet.', 'db-app-builder'); ?></p>
                    <?php else: ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Title', 'db-app-builder'); ?></th>
                                    <th><?php _e('Base Form', 'db-app-builder'); ?></th>
                                    <th><?php _e('Steps', 'db-app-builder'); ?></th>
                                    <th><?php _e('Status', 'db-app-builder'); ?></th>
                                    <th><?php _e('Created', 'db-app-builder'); ?></th>
                                    <th><?php _e('Actions', 'db-app-builder'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($wizard_forms as $wizard): ?>
                                    <?php
                                    $base_form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $wizard->form_id));
                                    $steps_config = json_decode($wizard->steps_config, true);
                                    $step_count = is_array($steps_config) ? count($steps_config) : 0;
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo esc_html($wizard->title); ?></strong>
                                            <?php if (!empty($wizard->description)): ?>
                                                <br><small><?php echo esc_html(wp_trim_words($wizard->description, 10)); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $base_form ? esc_html($base_form->form_title) : __('Form not found', 'db-app-builder'); ?>
                                        </td>
                                        <td><?php echo $step_count; ?> <?php _e('steps', 'db-app-builder'); ?></td>
                                        <td>
                                            <span class="dab-status-badge dab-status-<?php echo $wizard->is_active ? 'active' : 'inactive'; ?>">
                                                <?php echo $wizard->is_active ? __('Active', 'db-app-builder') : __('Inactive', 'db-app-builder'); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($wizard->created_at)); ?></td>
                                        <td>
                                            <a href="#" class="button button-small dab-edit-wizard" data-wizard-id="<?php echo $wizard->id; ?>">
                                                <?php _e('Edit', 'db-app-builder'); ?>
                                            </a>
                                            <a href="<?php echo admin_url('admin.php?page=dab_form_builder&form_id=' . $wizard->form_id); ?>" class="button button-small">
                                                <?php _e('Edit Form', 'db-app-builder'); ?>
                                            </a>
                                            <a href="#" class="button button-small dab-preview-wizard" data-wizard-id="<?php echo $wizard->id; ?>">
                                                <?php _e('Preview', 'db-app-builder'); ?>
                                            </a>
                                            <a href="#" class="button button-small button-link-delete dab-delete-wizard" data-wizard-id="<?php echo $wizard->id; ?>">
                                                <?php _e('Delete', 'db-app-builder'); ?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                
                <!-- Shortcode Information -->
                <div class="dab-wizard-section">
                    <h2><?php _e('Using Multi-Step Forms', 'db-app-builder'); ?></h2>
                    <p><?php _e('Use the following shortcode to display a multi-step form:', 'db-app-builder'); ?></p>
                    <code>[dab_multistep_form form_id="FORM_ID"]</code>
                    <p><?php _e('Replace FORM_ID with the ID of the base form you want to display as a multi-step wizard.', 'db-app-builder'); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Edit Wizard Modal -->
        <div id="dab-edit-wizard-modal" class="dab-modal" style="display: none;">
            <div class="dab-modal-content">
                <div class="dab-modal-header">
                    <h3><?php _e('Edit Multi-Step Form', 'db-app-builder'); ?></h3>
                    <button type="button" class="dab-modal-close">&times;</button>
                </div>
                <div class="dab-modal-body">
                    <form id="dab-edit-wizard-form">
                        <input type="hidden" id="edit-wizard-id" name="wizard_id">
                        
                        <div class="dab-form-field">
                            <label for="edit-wizard-title"><?php _e('Title', 'db-app-builder'); ?></label>
                            <input type="text" id="edit-wizard-title" name="wizard_title" class="dab-form-control" required>
                        </div>
                        
                        <div class="dab-form-field">
                            <label for="edit-wizard-description"><?php _e('Description', 'db-app-builder'); ?></label>
                            <textarea id="edit-wizard-description" name="wizard_description" class="dab-form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="dab-form-field">
                            <label>
                                <input type="checkbox" id="edit-wizard-active" name="is_active" value="1">
                                <?php _e('Active', 'db-app-builder'); ?>
                            </label>
                        </div>
                        
                        <h4><?php _e('Step Configuration', 'db-app-builder'); ?></h4>
                        <div id="dab-steps-config">
                            <!-- Steps will be loaded here -->
                        </div>
                        
                        <button type="button" class="button dab-add-step"><?php _e('Add Step', 'db-app-builder'); ?></button>
                    </form>
                </div>
                <div class="dab-modal-footer">
                    <button type="button" class="button dab-modal-close"><?php _e('Cancel', 'db-app-builder'); ?></button>
                    <button type="button" class="button button-primary dab-save-wizard"><?php _e('Save Changes', 'db-app-builder'); ?></button>
                </div>
            </div>
        </div>
        
        <style>
        .dab-wizard-manager {
            max-width: 1200px;
        }
        
        .dab-wizard-section {
            background: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #ccd0d4;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        
        .dab-wizard-section h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .dab-status-badge {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .dab-status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .dab-status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .dab-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .dab-modal-content {
            background: white;
            border-radius: 4px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .dab-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .dab-modal-header h3 {
            margin: 0;
        }
        
        .dab-modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
        }
        
        .dab-modal-body {
            padding: 20px;
        }
        
        .dab-modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 15px 20px;
            border-top: 1px solid #ddd;
            background: #f9f9f9;
        }
        
        .dab-form-field {
            margin-bottom: 15px;
        }
        
        .dab-form-field label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .dab-form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Create wizard form
            $('#dab-create-wizard-form').on('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                formData.append('action', 'dab_create_wizard_form');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('<?php _e('Multi-step form created successfully!', 'db-app-builder'); ?>');
                            location.reload();
                        } else {
                            alert('Error: ' + (response.data || 'Unknown error'));
                        }
                    },
                    error: function() {
                        alert('<?php _e('Error creating multi-step form', 'db-app-builder'); ?>');
                    }
                });
            });
            
            // Edit wizard
            $('.dab-edit-wizard').on('click', function(e) {
                e.preventDefault();
                const wizardId = $(this).data('wizard-id');
                loadWizardForEdit(wizardId);
            });
            
            // Delete wizard
            $('.dab-delete-wizard').on('click', function(e) {
                e.preventDefault();
                if (confirm('<?php _e('Are you sure you want to delete this multi-step form?', 'db-app-builder'); ?>')) {
                    const wizardId = $(this).data('wizard-id');
                    deleteWizard(wizardId);
                }
            });
            
            // Close modal
            $('.dab-modal-close').on('click', function() {
                $(this).closest('.dab-modal').hide();
            });
            
            // Save wizard changes
            $('.dab-save-wizard').on('click', function() {
                saveWizardChanges();
            });
            
            function loadWizardForEdit(wizardId) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'dab_get_wizard_config',
                        wizard_id: wizardId,
                        nonce: '<?php echo wp_create_nonce('dab_wizard_manager'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            populateEditForm(response.data);
                            $('#dab-edit-wizard-modal').show();
                        } else {
                            alert('Error loading wizard configuration');
                        }
                    }
                });
            }
            
            function populateEditForm(data) {
                $('#edit-wizard-id').val(data.id);
                $('#edit-wizard-title').val(data.title);
                $('#edit-wizard-description').val(data.description);
                $('#edit-wizard-active').prop('checked', data.is_active == 1);
                
                // Load steps configuration
                // This would be implemented based on the specific step configuration format
            }
            
            function saveWizardChanges() {
                const formData = new FormData(document.getElementById('dab-edit-wizard-form'));
                formData.append('action', 'dab_update_wizard_config');
                formData.append('nonce', '<?php echo wp_create_nonce('dab_wizard_manager'); ?>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('<?php _e('Wizard updated successfully!', 'db-app-builder'); ?>');
                            $('#dab-edit-wizard-modal').hide();
                            location.reload();
                        } else {
                            alert('Error: ' + (response.data || 'Unknown error'));
                        }
                    }
                });
            }
            
            function deleteWizard(wizardId) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'dab_delete_wizard_form',
                        wizard_id: wizardId,
                        nonce: '<?php echo wp_create_nonce('dab_wizard_manager'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('<?php _e('Wizard deleted successfully!', 'db-app-builder'); ?>');
                            location.reload();
                        } else {
                            alert('Error: ' + (response.data || 'Unknown error'));
                        }
                    }
                });
            }
        });
        </script>
        <?php
    }
    
    /**
     * AJAX handler to create wizard form
     */
    public static function create_wizard_form() {
        check_ajax_referer('dab_wizard_manager', 'dab_wizard_nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }
        
        $base_form_id = intval($_POST['base_form_id']);
        $wizard_title = sanitize_text_field($_POST['wizard_title']);
        $wizard_description = sanitize_textarea_field($_POST['wizard_description']);
        $fields_per_step = intval($_POST['fields_per_step']) ?: 5;
        
        if (!$base_form_id || !$wizard_title) {
            wp_send_json_error(__('Required fields are missing', 'db-app-builder'));
        }
        
        // Get form fields to create steps
        global $wpdb;
        $fields_table = $wpdb->prefix . 'dab_form_fields';
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE form_id = %d ORDER BY field_order ASC",
            $base_form_id
        ));
        
        if (empty($fields)) {
            wp_send_json_error(__('No fields found in the selected form', 'db-app-builder'));
        }
        
        // Create steps configuration
        $steps = array();
        $current_step = array(
            'title' => __('Step 1', 'db-app-builder'),
            'description' => '',
            'fields' => array()
        );
        
        $field_count = 0;
        foreach ($fields as $field) {
            if ($field_count >= $fields_per_step) {
                $steps[] = $current_step;
                $step_number = count($steps) + 1;
                $current_step = array(
                    'title' => sprintf(__('Step %d', 'db-app-builder'), $step_number),
                    'description' => '',
                    'fields' => array()
                );
                $field_count = 0;
            }
            
            $current_step['fields'][] = $field->id;
            $field_count++;
        }
        
        if (!empty($current_step['fields'])) {
            $steps[] = $current_step;
        }
        
        // Create wizard settings
        $settings = array(
            'show_progress' => isset($_POST['show_progress']),
            'save_progress' => isset($_POST['save_progress']),
            'allow_back' => isset($_POST['allow_back']),
            'step_validation' => isset($_POST['step_validation'])
        );
        
        // Insert wizard form
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        $result = $wpdb->insert($multistep_forms_table, array(
            'form_id' => $base_form_id,
            'title' => $wizard_title,
            'description' => $wizard_description,
            'steps_config' => json_encode($steps),
            'conditional_rules' => json_encode(array()),
            'settings' => json_encode($settings),
            'created_by' => get_current_user_id()
        ));
        
        if ($result !== false) {
            wp_send_json_success(array(
                'wizard_id' => $wpdb->insert_id,
                'message' => __('Multi-step form created successfully', 'db-app-builder')
            ));
        } else {
            wp_send_json_error(__('Failed to create multi-step form', 'db-app-builder'));
        }
    }
    
    /**
     * AJAX handler to update wizard configuration
     */
    public static function update_wizard_config() {
        check_ajax_referer('dab_wizard_manager', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }
        
        $wizard_id = intval($_POST['wizard_id']);
        $wizard_title = sanitize_text_field($_POST['wizard_title']);
        $wizard_description = sanitize_textarea_field($_POST['wizard_description']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        if (!$wizard_id || !$wizard_title) {
            wp_send_json_error(__('Required fields are missing', 'db-app-builder'));
        }
        
        global $wpdb;
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        
        $result = $wpdb->update($multistep_forms_table, array(
            'title' => $wizard_title,
            'description' => $wizard_description,
            'is_active' => $is_active
        ), array('id' => $wizard_id));
        
        if ($result !== false) {
            wp_send_json_success(__('Wizard updated successfully', 'db-app-builder'));
        } else {
            wp_send_json_error(__('Failed to update wizard', 'db-app-builder'));
        }
    }
    
    /**
     * AJAX handler to get wizard configuration
     */
    public static function get_wizard_config() {
        check_ajax_referer('dab_wizard_manager', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }
        
        $wizard_id = intval($_POST['wizard_id']);
        
        if (!$wizard_id) {
            wp_send_json_error(__('Invalid wizard ID', 'db-app-builder'));
        }
        
        global $wpdb;
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        
        $wizard = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $multistep_forms_table WHERE id = %d",
            $wizard_id
        ));
        
        if ($wizard) {
            wp_send_json_success($wizard);
        } else {
            wp_send_json_error(__('Wizard not found', 'db-app-builder'));
        }
    }
    
    /**
     * AJAX handler to delete wizard form
     */
    public static function delete_wizard_form() {
        check_ajax_referer('dab_wizard_manager', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }
        
        $wizard_id = intval($_POST['wizard_id']);
        
        if (!$wizard_id) {
            wp_send_json_error(__('Invalid wizard ID', 'db-app-builder'));
        }
        
        global $wpdb;
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        
        $result = $wpdb->delete($multistep_forms_table, array('id' => $wizard_id));
        
        if ($result !== false) {
            wp_send_json_success(__('Wizard deleted successfully', 'db-app-builder'));
        } else {
            wp_send_json_error(__('Failed to delete wizard', 'db-app-builder'));
        }
    }
}
